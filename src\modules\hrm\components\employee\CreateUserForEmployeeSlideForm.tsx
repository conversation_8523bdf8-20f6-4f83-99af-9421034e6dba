import React from 'react';

import { Card } from '@/shared';
import CreateUserForEmployeeForm from './CreateUserForEmployeeForm';

interface CreateUserForEmployeeSlideFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  employeeId?: number;
}

/**
 * Component form tạo tài khoản người dùng cho nhân viên trong SlideInForm
 */
const CreateUserForEmployeeSlideForm: React.FC<CreateUserForEmployeeSlideFormProps> = ({
  onSuccess,
  onCancel,
  employeeId,
}) => {
  return (
    <Card>
      <CreateUserForEmployeeForm
        onSuccess={onSuccess}
        onCancel={onCancel}
        employeeId={employeeId}
      />
    </Card>
  );
};

export default CreateUserForEmployeeSlideForm;
