import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  Form,
  FormGrid,
  FormItem,
  Icon,
  Input,
  SearchInputWithLazyLoading,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useFormErrors } from '@/shared/hooks';

import { useCreateUserForEmployee } from '../../hooks/useEmployeeUser';
import { searchEmployees } from '../../hooks/useEmployees';
import { createUserForEmployeeSchema } from '../../schemas/employee-user.schema';

// Props cho component
interface CreateUserForEmployeeFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  employeeId?: number;
}

/**
 * Form tạo tài khoản người dùng cho nhân viên
 */
const CreateUserForEmployeeForm: React.FC<CreateUserForEmployeeFormProps> = ({
  onSuccess,
  onCancel,
  employeeId,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const { formRef, setFormErrors } =
    useFormErrors<z.infer<ReturnType<typeof createUserForEmployeeSchema>>>();
  const createUserMutation = useCreateUserForEmployee();
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);

  // Schema cho form
  const schema = createUserForEmployeeSchema(t);

  // Xử lý submit form
  const handleSubmit = (values: unknown) => {
    try {
      // Reset form errors
      setFormErrors({});

      // Type assertion to the correct type
      const formValues = values as z.infer<ReturnType<typeof createUserForEmployeeSchema>>;

      // Prepare data for API
      const submitData = {
        username: formValues.username,
        password: formValues.password,
        email: formValues.email,
        fullName: formValues.fullName,
        employeeId: employeeId || formValues.employeeId,
      };

      // Call API to create user
      createUserMutation.mutate(submitData, {
        onSuccess: () => {
          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: any) => {
          // Handle API errors
          if (error?.response?.data?.message) {
            // Show general error
            setFormErrors({
              _error: error.response.data.message,
            });
          }

          // Handle field-specific errors
          if (error?.response?.data?.errors) {
            setFormErrors(error.response.data.errors);
          }
        },
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Form
      ref={formRef as React.RefObject<FormRef<FieldValues>>}
      schema={schema}
      onSubmit={handleSubmit}
      className="space-y-6"
    >
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-6">
          {t('hrm:employee.form.createUserTitle', 'Tạo tài khoản người dùng cho nhân viên')}
        </h2>

        {/* Display general error if any */}
        {formRef.current?.getFormState().errors._error && (
          <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md">
            {String(formRef.current?.getFormState().errors._error.message)}
          </div>
        )}

        <div className="space-y-6">
          <FormItem
            name="username"
            label={t('hrm:employee.form.username', 'Tên đăng nhập')}
            required
          >
            <Input
              placeholder={t('hrm:employee.form.usernamePlaceholder', 'Nhập tên đăng nhập')}
              leftIcon={<Icon name="user" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormGrid columns={2}>
            <FormItem name="password" label={t('hrm:employee.form.password', 'Mật khẩu')} required>
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder={t('hrm:employee.form.passwordPlaceholder', 'Nhập mật khẩu')}
                leftIcon={<Icon name="lock" size="sm" />}
                rightIcon={
                  <div className="cursor-pointer" onClick={() => setShowPassword(!showPassword)}>
                    <Icon name={showPassword ? 'eye-off' : 'eye'} size="sm" />
                  </div>
                }
                fullWidth
              />
            </FormItem>

            <FormItem
              name="confirmPassword"
              label={t('hrm:employee.form.confirmPassword', 'Xác nhận mật khẩu')}
              required
            >
              <Input
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder={t('hrm:employee.form.confirmPasswordPlaceholder', 'Xác nhận mật khẩu')}
                leftIcon={<Icon name="lock" size="sm" />}
                rightIcon={
                  <div
                    className="cursor-pointer"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Icon name={showConfirmPassword ? 'eye-off' : 'eye'} size="sm" />
                  </div>
                }
                fullWidth
              />
            </FormItem>
          </FormGrid>

          <FormItem name="email" label={t('hrm:employee.form.email', 'Email')} required>
            <Input
              type="email"
              placeholder={t('hrm:employee.form.emailPlaceholder', 'Nhập địa chỉ email')}
              leftIcon={<Icon name="mail" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="fullName"
            label={t('hrm:employee.form.fullName', 'Họ và tên đầy đủ')}
            required
          >
            <Input
              placeholder={t('hrm:employee.form.fullNamePlaceholder', 'Nhập họ và tên đầy đủ')}
              leftIcon={<Icon name="user" size="sm" />}
              fullWidth
            />
          </FormItem>

          <FormItem name="employeeId" label={t('hrm:employee.form.employee', 'Nhân viên')}>
            <SearchInputWithLazyLoading
              loadOptions={searchEmployees}
              placeholder={t('hrm:employee.form.employeePlaceholder', 'Tìm kiếm nhân viên...')}
              onChange={value => {
                if (formRef.current) {
                  (formRef.current as any).setValue('employeeId', Number(value));
                }
              }}
              debounceTime={300}
              noResultsText={t('common:noResults', 'Không tìm thấy kết quả')}
              loadingText={t('common:loading', 'Đang tìm kiếm...')}
              loadInitialOptions={true}
              fullWidth
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          {onCancel && (
            <Button variant="outline" onClick={onCancel} disabled={createUserMutation.isPending}>
              {t('common:cancel', 'Hủy')}
            </Button>
          )}
          <Button
            type="submit"
            loading={createUserMutation.isPending}
            disabled={createUserMutation.isPending}
          >
            {t('common:save', 'Lưu')}
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default CreateUserForEmployeeForm;
